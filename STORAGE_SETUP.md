# Storage Setup for Jazz & Dine

This guide will help you set up the Supabase storage bucket for image uploads in the Jazz & Dine application.

## Quick Setup

### Option 1: Using Supabase Dashboard (Recommended)

1. **Go to your Supabase project dashboard**
2. **Navigate to Storage** in the left sidebar
3. **Create a new bucket:**
   - Click "New bucket"
   - Name: `media`
   - Make it **Public** (check the public checkbox)
   - Click "Create bucket"

4. **Disable RLS for simple setup:**
   - Go to **SQL Editor** in your Supabase dashboard
   - Run these queries one by one:
   ```sql
   -- Drop all existing policies
   DO $$
   DECLARE
       policy_record RECORD;
   BEGIN
       FOR policy_record IN
           SELECT policyname
           FROM pg_policies
           WHERE schemaname = 'storage' AND tablename = 'objects'
       LOOP
           EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON storage.objects';
       END LOOP;
   END $$;

   -- Disable RLS completely
   ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;
   ```

### Option 2: Using SQL Script

1. **Go to SQL Editor** in your Supabase dashboard
2. **Copy and paste** the contents of `database/storage-setup.sql`
3. **Run the script**

## Verification

After setup, you should be able to:
- Upload images through the admin forms
- See uploaded images in the Supabase Storage dashboard
- Access uploaded images via their public URLs

## Troubleshooting

### If uploads still fail:

1. **Check bucket exists:**
   ```sql
   SELECT * FROM storage.buckets WHERE id = 'media';
   ```

2. **Verify RLS is disabled:**
   ```sql
   SELECT tablename, rowsecurity
   FROM pg_tables
   WHERE schemaname = 'storage' AND tablename = 'objects';
   ```
   (rowsecurity should be false)

3. **Check for remaining policies:**
   ```sql
   SELECT * FROM pg_policies WHERE schemaname = 'storage' AND tablename = 'objects';
   ```
   (should return no rows)

4. **Check bucket is public:**
   - In Supabase Dashboard > Storage > media bucket
   - Ensure "Public" toggle is ON

5. **Last resort - Create permissive policy:**
   If RLS can't be disabled, run:
   ```sql
   ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
   CREATE POLICY "Allow all operations" ON storage.objects FOR ALL USING (true);
   ```

### Alternative: Use URL input

If upload continues to fail, you can:
- Use the "Add URL" button in forms
- Upload images to any image hosting service (Imgur, Cloudinary, etc.)
- Paste the public URL into the form

## Security Note

This setup disables RLS for simplicity during development. For production, consider implementing proper RLS policies to restrict access based on user authentication and permissions.

## File Naming

Uploaded files are automatically renamed with timestamps and random strings to prevent conflicts:
- Format: `{timestamp}-{random}.{extension}`
- Example: `1703123456789-abc123.jpg`
