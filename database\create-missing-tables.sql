-- Complete database setup for Jazz & Dine application
-- Run this ENTIRE script in your Supabase SQL editor

-- =====================================================
-- STEP 1: Create all required tables
-- =====================================================

-- Create venues table if it doesn't exist, or add missing columns
CREATE TABLE IF NOT EXISTS venues (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns to venues table if they don't exist
DO $$
BEGIN
    -- Add description column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'venues' AND column_name = 'description') THEN
        ALTER TABLE venues ADD COLUMN description TEXT;
    END IF;

    -- Add logo_url column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'venues' AND column_name = 'logo_url') THEN
        ALTER TABLE venues ADD COLUMN logo_url TEXT;
    END IF;

    -- Add image_url column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'venues' AND column_name = 'image_url') THEN
        ALTER TABLE venues ADD COLUMN image_url TEXT;
    END IF;

    -- Add map_link column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'venues' AND column_name = 'map_link') THEN
        ALTER TABLE venues ADD COLUMN map_link TEXT;
    END IF;
END $$;

-- Create artists table if it doesn't exist, or add missing columns
CREATE TABLE IF NOT EXISTS artists (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns to artists table if they don't exist
DO $$
BEGIN
    -- Add bio column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'artists' AND column_name = 'bio') THEN
        ALTER TABLE artists ADD COLUMN bio TEXT;
    END IF;

    -- Add image_url column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'artists' AND column_name = 'image_url') THEN
        ALTER TABLE artists ADD COLUMN image_url TEXT;
    END IF;

    -- Add instagram column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'artists' AND column_name = 'instagram') THEN
        ALTER TABLE artists ADD COLUMN instagram TEXT;
    END IF;

    -- Add facebook column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'artists' AND column_name = 'facebook') THEN
        ALTER TABLE artists ADD COLUMN facebook TEXT;
    END IF;

    -- Add youtube column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'artists' AND column_name = 'youtube') THEN
        ALTER TABLE artists ADD COLUMN youtube TEXT;
    END IF;

    -- Add website column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'artists' AND column_name = 'website') THEN
        ALTER TABLE artists ADD COLUMN website TEXT;
    END IF;
END $$;

-- Create events table if it doesn't exist
CREATE TABLE IF NOT EXISTS events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    short_description TEXT,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    venue_id UUID REFERENCES venues(id) ON DELETE SET NULL,
    cover_image_url TEXT,
    event_type TEXT NOT NULL DEFAULT 'dinner_show',
    single_ticket_price DECIMAL(10,2) NOT NULL DEFAULT 125.00,
    group_ticket_price DECIMAL(10,2) NOT NULL DEFAULT 100.00,
    max_capacity INTEGER NOT NULL DEFAULT 100,
    status TEXT NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create event_artists junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS event_artists (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id UUID REFERENCES events(id) ON DELETE CASCADE,
    artist_id UUID REFERENCES artists(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(event_id, artist_id)
);

-- Create menu_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS menu_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id UUID REFERENCES events(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT,
    image_url TEXT,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create drink_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS drink_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id UUID REFERENCES events(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    category TEXT,
    image_url TEXT,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tickets table if it doesn't exist
CREATE TABLE IF NOT EXISTS tickets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id UUID REFERENCES events(id) ON DELETE CASCADE,
    customer_name TEXT NOT NULL,
    customer_email TEXT NOT NULL,
    ticket_type TEXT NOT NULL DEFAULT 'single',
    quantity INTEGER NOT NULL DEFAULT 1,
    total_price DECIMAL(10,2) NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_menu_items_event_id ON menu_items(event_id);
CREATE INDEX IF NOT EXISTS idx_drink_items_event_id ON drink_items(event_id);
CREATE INDEX IF NOT EXISTS idx_event_artists_event_id ON event_artists(event_id);
CREATE INDEX IF NOT EXISTS idx_event_artists_artist_id ON event_artists(artist_id);
CREATE INDEX IF NOT EXISTS idx_tickets_event_id ON tickets(event_id);

-- Enable RLS on new tables (optional - you can disable if you prefer)
ALTER TABLE menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE drink_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_artists ENABLE ROW LEVEL SECURITY;
ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;

-- Create simple policies (allow all operations for now)
CREATE POLICY "Allow all operations on menu_items" ON menu_items FOR ALL USING (true);
CREATE POLICY "Allow all operations on drink_items" ON drink_items FOR ALL USING (true);
CREATE POLICY "Allow all operations on event_artists" ON event_artists FOR ALL USING (true);
CREATE POLICY "Allow all operations on tickets" ON tickets FOR ALL USING (true);

-- Alternative: Disable RLS completely (simpler approach)
-- ALTER TABLE menu_items DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE drink_items DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE event_artists DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE tickets DISABLE ROW LEVEL SECURITY;
