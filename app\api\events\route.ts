import { NextRequest, NextResponse } from 'next/server';
import { eventService } from '@/lib/database';
import type { CreateEventForm } from '@/lib/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeRelations = searchParams.get('include') === 'relations';
    const upcoming = searchParams.get('upcoming') === 'true';
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

    let events;
    if (upcoming) {
      events = await eventService.getUpcoming(limit);
    } else {
      events = await eventService.getAll(includeRelations);
    }

    return NextResponse.json({ data: events });
  } catch (error) {
    console.error('Error fetching events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: CreateEventForm = await request.json();
    
    // Basic validation
    if (!body.name || !body.date || !body.venue_id) {
      return NextResponse.json(
        { error: 'Missing required fields: name, date, venue_id' },
        { status: 400 }
      );
    }

    const event = await eventService.create(body);
    return NextResponse.json({ data: event }, { status: 201 });
  } catch (error) {
    console.error('Error creating event:', error);
    return NextResponse.json(
      { error: 'Failed to create event' },
      { status: 500 }
    );
  }
}
