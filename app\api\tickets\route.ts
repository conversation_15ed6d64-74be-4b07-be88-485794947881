import { NextRequest, NextResponse } from 'next/server';
import { ticketService } from '@/lib/database';
import type { CreateTicketForm } from '@/lib/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const eventId = searchParams.get('event_id');

    if (eventId) {
      const tickets = await ticketService.getByEventId(eventId);
      return NextResponse.json({ data: tickets });
    }

    const result = await ticketService.getAll(page, limit);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching tickets:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tickets' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: CreateTicketForm = await request.json();
    
    // Basic validation
    if (!body.event_id || !body.customer_name || !body.customer_email || !body.ticket_type) {
      return NextResponse.json(
        { error: 'Missing required fields: event_id, customer_name, customer_email, ticket_type' },
        { status: 400 }
      );
    }

    // Validate group ticket requirements
    if (body.ticket_type === 'group') {
      if (!body.group_name || body.group_size < 3) {
        return NextResponse.json(
          { error: 'Group tickets require group_name and group_size >= 3' },
          { status: 400 }
        );
      }
    }

    const ticket = await ticketService.create(body);
    return NextResponse.json({ data: ticket }, { status: 201 });
  } catch (error) {
    console.error('Error creating ticket:', error);
    return NextResponse.json(
      { error: 'Failed to create ticket' },
      { status: 500 }
    );
  }
}
