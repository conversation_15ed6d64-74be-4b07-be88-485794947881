"use client"

import { Users, Share2 } from "lucide-react"
import type { Event } from "@/lib/types"

interface TicketTypeSelectionProps {
  event: Event
  onSelectType: (type: 'single' | 'group') => void
}

export default function TicketTypeSelection({ event, onSelectType }: TicketTypeSelectionProps) {
  return (
    <div className="space-y-4 mb-8">
      <div
        className="bg-white/80 backdrop-blur-sm p-6 hover:bg-white hover:shadow-xl transition-all duration-300 cursor-pointer rounded-2xl border border-purple-100/50 transform hover:scale-[1.02] animate-in slide-in-from-left-4 duration-500"
        onClick={() => onSelectType('single')}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
              <Users className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="font-serif font-semibold text-gray-900 text-lg mb-1">Single Ticket</h3>
              <p className="text-gray-600">1–2 guests</p>
              <p className="text-gray-600 text-sm">Perfect for an intimate evening</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              ${event.single_ticket_price}
            </div>
            <div className="text-sm text-gray-500">per person</div>
          </div>
        </div>
      </div>

      <div
        className="bg-white/80 backdrop-blur-sm p-6 hover:bg-white hover:shadow-xl transition-all duration-300 cursor-pointer rounded-2xl border border-blue-100/50 transform hover:scale-[1.02] animate-in slide-in-from-right-4 duration-500 delay-150"
        onClick={() => onSelectType('group')}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
              <Share2 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="font-serif font-semibold text-gray-900 text-lg mb-1">Group Table</h3>
              <p className="text-gray-600">3 and more guests</p>
              <p className="text-gray-600 text-sm">Share the experience with friends</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              ${event.group_ticket_price}
            </div>
            <div className="text-sm text-gray-500">per person</div>
          </div>
        </div>
      </div>
    </div>
  )
}
