import { NextResponse } from 'next/server';
import { dashboardService } from '@/lib/database';

export async function GET() {
  try {
    const stats = await dashboardService.getStats();
    return NextResponse.json({ data: stats });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard stats' },
      { status: 500 }
    );
  }
}
