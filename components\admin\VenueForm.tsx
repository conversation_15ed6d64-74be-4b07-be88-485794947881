"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Upload, MapPin, Building, Image, ExternalLink } from "lucide-react"
import { toast } from "sonner"
import MediaUpload from "./MediaUpload"
import { validateVenue, formatZodErrors } from "@/lib/validations"
import type { Venue, CreateVenueForm } from "@/lib/types"

interface VenueFormProps {
  venue?: Venue
  onSubmit: (data: CreateVenueForm) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export default function VenueForm({ venue, onSubmit, onCancel, isLoading = false }: VenueFormProps) {
  const [formData, setFormData] = useState<CreateVenueForm>({
    name: venue?.name || "",
    address: venue?.address || "",
    logo_url: venue?.logo_url || "",
    image_url: venue?.image_url || "",
    map_link: venue?.map_link || "",
    description: venue?.description || ""
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = (): boolean => {
    const result = validateVenue(formData)

    if (!result.success) {
      const formattedErrors = formatZodErrors(result.error)
      setErrors(formattedErrors)
      return false
    }

    setErrors({})
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    console.log("VenueForm - Starting submission with data:", formData)

    if (!validateForm()) {
      console.log("VenueForm - Validation failed")
      toast.error("Please fix the form errors")
      return
    }

    try {
      console.log("VenueForm - Calling onSubmit with:", formData)
      await onSubmit(formData)
      console.log("VenueForm - Submission successful")
      toast.success(venue ? "Venue updated successfully" : "Venue created successfully")
    } catch (error) {
      console.error("VenueForm - Error submitting venue:", error)

      let errorMessage = "Failed to save venue"
      if (error instanceof Error) {
        errorMessage = error.message
        console.error("VenueForm - Error details:", error.stack)
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = `Failed to save venue: ${JSON.stringify(error)}`
        console.error("VenueForm - Error object:", error)
      }

      toast.error(errorMessage)
    }
  }

  const handleInputChange = (field: keyof CreateVenueForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="w-5 h-5" />
          {venue ? "Edit Venue" : "Add New Venue"}
        </CardTitle>
        <CardDescription>
          {venue ? "Update venue information" : "Create a new venue for your events"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Venue Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Enter venue name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Address *</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  placeholder="Enter venue address"
                  className={errors.address ? "border-red-500" : ""}
                />
                {errors.address && (
                  <p className="text-sm text-red-500">{errors.address}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                placeholder="Enter venue description (optional)"
                rows={3}
              />
            </div>
          </div>

          {/* Images */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Images</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <MediaUpload
                label="Venue Logo"
                value={formData.logo_url}
                onChange={(url) => handleInputChange("logo_url", url)}
                placeholder="Upload venue logo or enter URL"
                maxSize={2}
              />

              <MediaUpload
                label="Gallery Image"
                value={formData.image_url}
                onChange={(url) => handleInputChange("image_url", url)}
                placeholder="Upload venue image or enter URL"
                maxSize={5}
              />
            </div>
          </div>

          {/* Map Link */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Location</h3>
            
            <div className="space-y-2">
              <Label htmlFor="map_link" className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Google Maps Link
              </Label>
              <Input
                id="map_link"
                value={formData.map_link}
                onChange={(e) => handleInputChange("map_link", e.target.value)}
                placeholder="https://maps.google.com/..."
                className={errors.map_link ? "border-red-500" : ""}
              />
              {errors.map_link && (
                <p className="text-sm text-red-500">{errors.map_link}</p>
              )}
              <p className="text-sm text-gray-500">
                Add a Google Maps link to help customers find your venue
              </p>
            </div>
          </div>



          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-amber-600 hover:bg-amber-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {venue ? "Updating..." : "Creating..."}
                </>
              ) : (
                venue ? "Update Venue" : "Create Venue"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
