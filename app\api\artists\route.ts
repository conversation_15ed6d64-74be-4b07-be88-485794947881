import { NextRequest, NextResponse } from 'next/server';
import { artistService } from '@/lib/database';
import type { CreateArtistForm } from '@/lib/types';

export async function GET() {
  try {
    const artists = await artistService.getAll();
    return NextResponse.json({ data: artists });
  } catch (error) {
    console.error('Error fetching artists:', error);
    return NextResponse.json(
      { error: 'Failed to fetch artists' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: CreateArtistForm = await request.json();
    
    // Basic validation
    if (!body.name || !body.role) {
      return NextResponse.json(
        { error: 'Missing required fields: name, role' },
        { status: 400 }
      );
    }

    const artist = await artistService.create(body);
    return NextResponse.json({ data: artist }, { status: 201 });
  } catch (error) {
    console.error('Error creating artist:', error);
    return NextResponse.json(
      { error: 'Failed to create artist' },
      { status: 500 }
    );
  }
}
