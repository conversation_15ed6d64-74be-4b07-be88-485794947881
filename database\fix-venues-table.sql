-- URGENT FIX: Add missing columns to venues table
-- Run this immediately in Supabase SQL Editor to fix venue creation

-- Add missing columns to venues table
DO $$ 
BEGIN
    -- Add description column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'venues' AND column_name = 'description') THEN
        ALTER TABLE venues ADD COLUMN description TEXT;
        RAISE NOTICE 'Added description column to venues table';
    ELSE
        RAISE NOTICE 'Description column already exists in venues table';
    END IF;
    
    -- Add logo_url column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'venues' AND column_name = 'logo_url') THEN
        ALTER TABLE venues ADD COLUMN logo_url TEXT;
        RAISE NOTICE 'Added logo_url column to venues table';
    ELSE
        RAISE NOTICE 'Logo_url column already exists in venues table';
    END IF;
    
    -- Add image_url column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'venues' AND column_name = 'image_url') THEN
        ALTER TABLE venues ADD COLUMN image_url TEXT;
        RAISE NOTICE 'Added image_url column to venues table';
    ELSE
        RAISE NOTICE 'Image_url column already exists in venues table';
    END IF;
    
    -- Add map_link column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'venues' AND column_name = 'map_link') THEN
        ALTER TABLE venues ADD COLUMN map_link TEXT;
        RAISE NOTICE 'Added map_link column to venues table';
    ELSE
        RAISE NOTICE 'Map_link column already exists in venues table';
    END IF;
END $$;

-- Verify the table structure
SELECT 
    'Venues table structure after fix' as info,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'venues' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Test venue insertion to make sure it works
INSERT INTO venues (name, address, description, logo_url, image_url, map_link) 
VALUES (
    'Test Venue Fix', 
    '123 Test Street', 
    'Test description to verify fix',
    'https://example.com/logo.jpg',
    'https://example.com/image.jpg',
    'https://maps.google.com/test'
)
ON CONFLICT DO NOTHING;

-- Verify the test insertion worked
SELECT 'Test insertion result' as info, COUNT(*) as test_venues 
FROM venues 
WHERE name = 'Test Venue Fix';

-- Clean up test data
DELETE FROM venues WHERE name = 'Test Venue Fix';

-- Final confirmation
SELECT 'Fix completed successfully' as status;
