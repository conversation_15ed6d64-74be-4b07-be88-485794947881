"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Upload, User, Music, Instagram, Facebook, Youtube, Globe } from "lucide-react"
import { toast } from "sonner"
import MediaUpload from "./MediaUpload"
import { validateArtist, formatZodErrors } from "@/lib/validations"
import type { Artist, CreateArtistForm } from "@/lib/types"

interface ArtistFormProps {
  artist?: Artist
  onSubmit: (data: CreateArtistForm) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export default function ArtistForm({ artist, onSubmit, onCancel, isLoading = false }: ArtistFormProps) {
  const [formData, setFormData] = useState<CreateArtistForm>({
    name: artist?.name || "",
    role: artist?.role || "",
    bio: artist?.bio || "",
    image_url: artist?.image_url || "",
    instagram: artist?.instagram || "",
    facebook: artist?.facebook || "",
    youtube: artist?.youtube || "",
    website: artist?.website || ""
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = (): boolean => {
    const result = validateArtist(formData)

    if (!result.success) {
      const formattedErrors = formatZodErrors(result.error)
      setErrors(formattedErrors)
      return false
    }

    setErrors({})
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Please fix the form errors")
      return
    }

    try {
      await onSubmit(formData)
      toast.success(artist ? "Artist updated successfully" : "Artist created successfully")
    } catch (error) {
      console.error("Error submitting artist:", error)
      toast.error("Failed to save artist")
    }
  }

  const handleInputChange = (field: keyof CreateArtistForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="w-5 h-5" />
          {artist ? "Edit Artist" : "Add New Artist"}
        </CardTitle>
        <CardDescription>
          {artist ? "Update artist information" : "Create a new artist profile for your events"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Artist Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Enter artist name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Position/Role *</Label>
                <Input
                  id="role"
                  value={formData.role}
                  onChange={(e) => handleInputChange("role", e.target.value)}
                  placeholder="e.g., Lead Vocalist, Guitarist, DJ"
                  className={errors.role ? "border-red-500" : ""}
                />
                {errors.role && (
                  <p className="text-sm text-red-500">{errors.role}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="bio">Biography</Label>
              <Textarea
                id="bio"
                value={formData.bio}
                onChange={(e) => handleInputChange("bio", e.target.value)}
                placeholder="Tell us about the artist's background and style..."
                rows={4}
              />
            </div>

            <MediaUpload
              label="Profile Image"
              value={formData.image_url}
              onChange={(url) => handleInputChange("image_url", url)}
              placeholder="Upload artist photo or enter URL"
              maxSize={3}
            />
          </div>

          {/* Social Media Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Social Media & Links</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="instagram" className="flex items-center gap-2">
                  <Instagram className="w-4 h-4" />
                  Instagram
                </Label>
                <Input
                  id="instagram"
                  value={formData.instagram}
                  onChange={(e) => handleInputChange("instagram", e.target.value)}
                  placeholder="https://instagram.com/username"
                  className={errors.instagram ? "border-red-500" : ""}
                />
                {errors.instagram && (
                  <p className="text-sm text-red-500">{errors.instagram}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="facebook" className="flex items-center gap-2">
                  <Facebook className="w-4 h-4" />
                  Facebook
                </Label>
                <Input
                  id="facebook"
                  value={formData.facebook}
                  onChange={(e) => handleInputChange("facebook", e.target.value)}
                  placeholder="https://facebook.com/username"
                  className={errors.facebook ? "border-red-500" : ""}
                />
                {errors.facebook && (
                  <p className="text-sm text-red-500">{errors.facebook}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="youtube" className="flex items-center gap-2">
                  <Youtube className="w-4 h-4" />
                  YouTube
                </Label>
                <Input
                  id="youtube"
                  value={formData.youtube}
                  onChange={(e) => handleInputChange("youtube", e.target.value)}
                  placeholder="https://youtube.com/channel/..."
                  className={errors.youtube ? "border-red-500" : ""}
                />
                {errors.youtube && (
                  <p className="text-sm text-red-500">{errors.youtube}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="website" className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  Website
                </Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  placeholder="https://artistwebsite.com"
                  className={errors.website ? "border-red-500" : ""}
                />
                {errors.website && (
                  <p className="text-sm text-red-500">{errors.website}</p>
                )}
              </div>
            </div>
          </div>



          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-amber-600 hover:bg-amber-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {artist ? "Updating..." : "Creating..."}
                </>
              ) : (
                artist ? "Update Artist" : "Create Artist"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
