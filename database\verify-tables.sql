-- Verify all required tables exist for Jazz & Dine application
-- Run this in your Supabase SQL editor to check table structure

-- Check if all required tables exist
SELECT 
    table_name,
    CASE 
        WHEN table_name IN ('venues', 'artists', 'events', 'event_artists', 'menu_items', 'tickets') 
        THEN '✅ Required'
        ELSE '❓ Optional'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- Check venues table structure
SELECT 'venues' as table_name, column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'venues' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check artists table structure  
SELECT 'artists' as table_name, column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'artists' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check events table structure
SELECT 'events' as table_name, column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'events' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check event_artists junction table
SELECT 'event_artists' as table_name, column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'event_artists' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check foreign key constraints
SELECT 
    tc.table_name, 
    tc.constraint_name, 
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_schema = 'public'
AND tc.table_name IN ('events', 'event_artists', 'menu_items', 'tickets')
ORDER BY tc.table_name, tc.constraint_name;

-- Check if RLS is enabled on tables
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN ('venues', 'artists', 'events', 'event_artists', 'menu_items', 'tickets')
ORDER BY tablename;
