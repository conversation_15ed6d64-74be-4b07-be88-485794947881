import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar, ArrowLeft } from "lucide-react"

export default function EventNotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-6">
        <div className="w-20 h-20 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <Calendar className="w-10 h-10 text-purple-600" />
        </div>
        
        <h1 className="text-3xl font-serif font-bold text-gray-900 mb-4">
          Event Not Found
        </h1>
        
        <p className="text-gray-600 mb-8 leading-relaxed">
          Sorry, we couldn't find the event you're looking for. It may have been moved, 
          cancelled, or the link might be incorrect.
        </p>
        
        <div className="space-y-4">
          <Link href="/">
            <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Events
            </Button>
          </Link>
          
          <Link href="/events">
            <Button variant="outline" className="w-full">
              Browse All Events
            </Button>
          </Link>
        </div>
        
        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Need help? Contact us at{" "}
            <a href="mailto:<EMAIL>" className="text-purple-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
