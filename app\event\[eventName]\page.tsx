"use client"

import { useState, useEffect, use } from "react"
import { notFound } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Wine } from "lucide-react"
import EventHero from "@/components/event/EventHero"
import VenueSection from "@/components/event/VenueSection"
import ArtistsSection from "@/components/event/ArtistsSection"
import MenuSection from "@/components/event/MenuSection"
import TicketTypeSelection from "@/components/booking/TicketTypeSelection"
import SingleTicketForm from "@/components/booking/SingleTicketForm"
import GroupTicketForm from "@/components/booking/GroupTicketForm"
import { slugToName } from "@/lib/utils"
import type { Event, Artist, MenuItem } from "@/lib/types"

interface EventPageProps {
  params: Promise<{
    eventName: string
  }>
}

export default function EventPage({ params }: EventPageProps) {
  const resolvedParams = use(params)
  const [event, setEvent] = useState<Event | null>(null)
  const [artists, setArtists] = useState<Artist[]>([])
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedTicketType, setSelectedTicketType] = useState<'single' | 'group' | ''>('')
  const [showTicketForm, setShowTicketForm] = useState(false)

  useEffect(() => {
    fetchEventData()
  }, [resolvedParams.eventName])

  const fetchEventData = async () => {
    try {
      // Convert URL slug back to event name for lookup
      const eventName = slugToName(decodeURIComponent(resolvedParams.eventName))

      // Fetch all events and find by name
      const eventsResponse = await fetch('/api/events?include=relations')
      const eventsResult = await eventsResponse.json()

      if (eventsResult.data) {
        const foundEvent = eventsResult.data.find((e: Event) =>
          e.name.toLowerCase() === eventName.toLowerCase()
        )

        if (!foundEvent) {
          notFound()
        }
        
        setEvent(foundEvent)
        
        // Extract artists from the event data
        if (foundEvent.artists) {
          const artistList = foundEvent.artists.map((ea: any) => ea.artist)
          setArtists(artistList)
        }
        
        // Set menu items
        if (foundEvent.menu_items) {
          setMenuItems(foundEvent.menu_items)
        }
      }
    } catch (error) {
      console.error('Error fetching event data:', error)
      notFound()
    } finally {
      setLoading(false)
    }
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
    }
  }

  const handleJoinClick = () => {
    scrollToSection("tickets")
  }

  const handleTicketTypeSelect = (type: 'single' | 'group') => {
    setSelectedTicketType(type)
    setShowTicketForm(true)
  }

  const handleTicketSubmit = async (ticketData: any) => {
    try {
      const response = await fetch('/api/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event_id: event?.id,
          ticket_type: selectedTicketType,
          customer_name: 'Demo Customer', // In real app, this would come from a form
          customer_email: '<EMAIL>',
          group_size: selectedTicketType === 'group' ? ticketData.groupSize : 1,
          group_name: selectedTicketType === 'group' ? ticketData.groupName : undefined,
          group_members: selectedTicketType === 'group' ? ticketData.groupMembers : undefined,
          dinner_selection: selectedTicketType === 'single' ? ticketData.dinnerSelection : undefined,
          selected_bottles: ticketData.selectedBottles?.map((bottleName: string) => {
            const bottle = menuItems.find(item => item.name === bottleName)
            return bottle ? {
              menu_item_id: bottle.id,
              quantity: 1,
              unit_price: Math.round((bottle.price || 0) * 0.85)
            } : null
          }).filter(Boolean) || [],
          total_amount: ticketData.totalAmount
        }),
      })

      const result = await response.json()

      if (result.data) {
        if (selectedTicketType === 'group') {
          alert(`Group table "${ticketData.groupName}" created successfully! Share link: ${window.location.origin}/join/${result.data.id}`)
        } else {
          alert('Ticket booked successfully! In a real app, this would redirect to payment.')
        }
      } else {
        alert('Booking failed: ' + result.error)
      }
    } catch (error) {
      console.error('Error booking ticket:', error)
      alert('Booking failed. Please try again.')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading event details...</p>
        </div>
      </div>
    )
  }

  if (!event) {
    return notFound()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30">
      {/* Event Hero */}
      <EventHero event={event} onJoinClick={handleJoinClick} />

      {/* Venue Section */}
      {event.venue && (
        <div className="px-4 sm:px-6 py-6">
          <VenueSection venue={event.venue} />
        </div>
      )}

      {/* Artists Section */}
      <ArtistsSection artists={artists} />

      {/* Menu Section */}
      <MenuSection menuItems={menuItems} />

      {/* Tickets Section */}
      <section id="tickets" className="px-4 sm:px-6 py-8 bg-gradient-to-br from-gray-50 via-purple-50/30 to-pink-50/30">
        <div className="flex items-center justify-between mb-6">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                <Wine className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-2xl font-serif font-bold text-gray-900">Secure Your Seat</h2>
            </div>
            <p className="text-gray-600">Choose your perfect dining experience</p>
          </div>
        </div>

        {/* Ticket Type Selection */}
        {!selectedTicketType && (
          <TicketTypeSelection event={event} onSelectType={handleTicketTypeSelect} />
        )}

        {/* Single Ticket Form */}
        {selectedTicketType === 'single' && showTicketForm && (
          <SingleTicketForm
            menuItems={menuItems}
            ticketPrice={event.single_ticket_price}
            onSubmit={handleTicketSubmit}
          />
        )}

        {/* Group Ticket Form */}
        {selectedTicketType === 'group' && showTicketForm && (
          <GroupTicketForm
            menuItems={menuItems}
            ticketPrice={event.group_ticket_price}
            onSubmit={handleTicketSubmit}
          />
        )}

        {/* Back Button */}
        {selectedTicketType && (
          <div className="mt-6">
            <Button
              onClick={() => {
                setSelectedTicketType('')
                setShowTicketForm(false)
              }}
              variant="outline"
              className="text-purple-600 border-purple-200 hover:bg-purple-50"
            >
              ← Back to Ticket Types
            </Button>
          </div>
        )}
      </section>

      {/* Bottom Navigation - Sticky */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-gray-200/50 z-50 shadow-2xl">
        <div className="flex items-center justify-around py-3 px-4">
          <button
            onClick={() => scrollToSection("about")}
            className="flex flex-col items-center gap-1 text-gray-600 hover:text-purple-600 transition-colors duration-200"
          >
            <div className="w-6 h-6 rounded-full bg-gray-200 hover:bg-purple-100 transition-colors duration-200"></div>
            <span className="text-xs font-medium">About</span>
          </button>
          <button
            onClick={() => scrollToSection("artists")}
            className="flex flex-col items-center gap-1 text-gray-600 hover:text-purple-600 transition-colors duration-200"
          >
            <div className="w-6 h-6 rounded-full bg-gray-200 hover:bg-purple-100 transition-colors duration-200"></div>
            <span className="text-xs font-medium">Artists</span>
          </button>
          <button
            onClick={() => scrollToSection("menu")}
            className="flex flex-col items-center gap-1 text-gray-600 hover:text-purple-600 transition-colors duration-200"
          >
            <div className="w-6 h-6 rounded-full bg-gray-200 hover:bg-purple-100 transition-colors duration-200"></div>
            <span className="text-xs font-medium">Menu</span>
          </button>
          <button
            onClick={() => scrollToSection("tickets")}
            className="flex flex-col items-center gap-1 text-white"
          >
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg flex items-center justify-center">
              <Wine className="w-4 h-4" />
            </div>
            <span className="text-xs font-medium text-purple-600">Book Now</span>
          </button>
        </div>
      </nav>

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-20"></div>
    </div>
  )
}