-- Storage bucket setup for Jazz & Dine application
-- Run this ENTIRE script in your Supabase SQL editor

-- Step 1: Create the media bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('media', 'media', true)
ON CONFLICT (id) DO NOTHING;

-- Step 2: Drop ALL existing policies on storage.objects
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN
        SELECT policyname
        FROM pg_policies
        WHERE schemaname = 'storage' AND tablename = 'objects'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON storage.objects';
    END LOOP;
END $$;

-- Step 3: Disable RLS completely on storage.objects
ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;

-- Step 4: Verify setup (run these queries to check)
-- SELECT * FROM storage.buckets WHERE id = 'media';
-- SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'storage' AND tablename = 'objects';

-- If you still get RLS errors after running this, try this alternative:
-- ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY "Allow all operations" ON storage.objects FOR ALL USING (true);
