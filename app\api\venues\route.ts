import { NextRequest, NextResponse } from 'next/server';
import { venueService } from '@/lib/database';
import type { CreateVenueForm } from '@/lib/types';

export async function GET() {
  try {
    const venues = await venueService.getAll();
    return NextResponse.json({ data: venues });
  } catch (error) {
    console.error('Error fetching venues:', error);
    return NextResponse.json(
      { error: 'Failed to fetch venues' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: CreateVenueForm = await request.json();
    
    // Basic validation
    if (!body.name || !body.address) {
      return NextResponse.json(
        { error: 'Missing required fields: name, address' },
        { status: 400 }
      );
    }

    const venue = await venueService.create(body);
    return NextResponse.json({ data: venue }, { status: 201 });
  } catch (error) {
    console.error('Error creating venue:', error);
    return NextResponse.json(
      { error: 'Failed to create venue' },
      { status: 500 }
    );
  }
}
