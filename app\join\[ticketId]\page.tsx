"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Wine,
  Check,
  UserPlus,
  Sparkles
} from "lucide-react"
import { formatEventDate, formatEventTime } from "@/lib/utils"

interface GroupTicket {
  id: string
  group_name: string
  group_size: number
  event: {
    name: string
    date: string
    start_time: string
    end_time: string
    venue: {
      name: string
      address: string
    }
  }
  group_members: Array<{
    id: string
    name: string
    email: string
    dinner_selection?: string
    has_confirmed: boolean
  }>
}

export default function JoinGroupPage() {
  const params = useParams()
  const ticketId = params.ticketId as string
  
  const [ticket, setTicket] = useState<GroupTicket | null>(null)
  const [loading, setLoading] = useState(true)
  const [joining, setJoining] = useState(false)
  const [joined, setJoined] = useState(false)
  const [error, setError] = useState("")
  
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    dinner_selection: ""
  })

  useEffect(() => {
    fetchGroupDetails()
  }, [ticketId])

  const fetchGroupDetails = async () => {
    try {
      const response = await fetch(`/api/tickets/${ticketId}/join`)
      const result = await response.json()
      
      if (result.data) {
        setTicket(result.data)
      } else {
        setError(result.error || "Group table not found")
      }
    } catch (error) {
      console.error('Error fetching group details:', error)
      setError("Failed to load group details")
    } finally {
      setLoading(false)
    }
  }

  const handleJoin = async () => {
    if (!formData.name || !formData.email) {
      setError("Please fill in your name and email")
      return
    }

    setJoining(true)
    setError("")

    try {
      const response = await fetch(`/api/tickets/${ticketId}/join`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()
      
      if (result.data) {
        setJoined(true)
        // Refresh the group details to show the new member
        await fetchGroupDetails()
      } else {
        setError(result.error || "Failed to join group")
      }
    } catch (error) {
      console.error('Error joining group:', error)
      setError("Failed to join group. Please try again.")
    } finally {
      setJoining(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading group details...</p>
        </div>
      </div>
    )
  }

  if (error && !ticket) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Group Not Found</h2>
            <p className="text-gray-600">{error}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (joined) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Welcome to the Group!</h2>
            <p className="text-gray-600 mb-4">
              You've successfully joined <strong>{ticket?.group_name}</strong>
            </p>
            <p className="text-sm text-gray-500">
              You'll receive confirmation details via email.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30">
      <div className="max-w-2xl mx-auto px-4 py-8">
        {/* Event Details */}
        <Card className="mb-6 bg-white/80 backdrop-blur-sm shadow-lg border-0">
          <CardHeader>
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="w-5 h-5 text-purple-600" />
              <CardTitle className="text-xl font-serif">Join Group Table</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h2 className="text-2xl font-serif font-bold text-gray-900 mb-2">
                  {ticket?.event.name}
                </h2>
                <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-purple-500" />
                    <span>{formatEventDate(ticket?.event.date || "")}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-purple-500" />
                    <span>{formatEventTime(ticket?.event.start_time || "", ticket?.event.end_time || "")}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-purple-500" />
                    <span>{ticket?.event.venue.name}</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-2xl">
                <h3 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                  <Wine className="w-4 h-4 text-blue-600" />
                  Table: {ticket?.group_name}
                </h3>
                <p className="text-sm text-gray-600">
                  {ticket?.group_members.length} of {ticket?.group_size} seats filled
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Members */}
        <Card className="mb-6 bg-white/80 backdrop-blur-sm shadow-lg border-0">
          <CardHeader>
            <CardTitle className="text-lg font-serif flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              Current Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {ticket?.group_members.map((member, index) => (
                <div key={member.id} className="flex items-center gap-3 p-3 bg-gradient-to-r from-gray-50 to-blue-50/50 rounded-2xl">
                  <Avatar className="w-10 h-10 ring-2 ring-blue-100">
                    <AvatarImage src="" alt={member.name} />
                    <AvatarFallback className="bg-gradient-to-br from-blue-100 to-purple-100">
                      {member.name.split(" ").map(n => n[0]).join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{member.name}</p>
                    <p className="text-sm text-gray-600">
                      {member.dinner_selection || "Dinner selection pending"}
                    </p>
                  </div>
                  {member.has_confirmed && (
                    <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                      <Check className="w-3 h-3 text-white" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Join Form */}
        {ticket && ticket.group_members.length < ticket.group_size && (
          <Card className="bg-white/90 backdrop-blur-sm shadow-xl border-0">
            <CardHeader>
              <CardTitle className="text-lg font-serif flex items-center gap-2">
                <UserPlus className="w-5 h-5 text-purple-600" />
                Join This Table
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name" className="text-sm font-semibold text-gray-700 mb-2 block">
                    Your Name
                  </Label>
                  <Input
                    id="name"
                    placeholder="Enter your full name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="h-12 text-base border-purple-200 focus:border-purple-400 focus:ring-purple-400"
                  />
                </div>

                <div>
                  <Label htmlFor="email" className="text-sm font-semibold text-gray-700 mb-2 block">
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="h-12 text-base border-purple-200 focus:border-purple-400 focus:ring-purple-400"
                  />
                </div>

                <div>
                  <Label htmlFor="dinner" className="text-sm font-semibold text-gray-700 mb-2 block">
                    Dinner Preference (Optional)
                  </Label>
                  <Input
                    id="dinner"
                    placeholder="Any dietary preferences or requests"
                    value={formData.dinner_selection}
                    onChange={(e) => setFormData({ ...formData, dinner_selection: e.target.value })}
                    className="h-12 text-base border-purple-200 focus:border-purple-400 focus:ring-purple-400"
                  />
                </div>

                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <p className="text-red-700 text-sm">{error}</p>
                  </div>
                )}

                <Button
                  onClick={handleJoin}
                  disabled={joining || !formData.name || !formData.email}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50"
                >
                  {joining ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Joining...
                    </>
                  ) : (
                    <>
                      <UserPlus className="w-5 h-5 mr-2" />
                      Join Table
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Table Full */}
        {ticket && ticket.group_members.length >= ticket.group_size && (
          <Card className="bg-white/90 backdrop-blur-sm shadow-xl border-0">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-amber-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Table is Full</h3>
              <p className="text-gray-600">
                This group table has reached its maximum capacity of {ticket.group_size} people.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
