-- Diagnostic script to check Jazz & Dine database structure
-- Run this in Supabase SQL Editor to diagnose issues

-- =====================================================
-- STEP 1: Check if all required tables exist
-- =====================================================

SELECT 
    'Table Existence Check' as check_type,
    table_name,
    CASE 
        WHEN table_name IN ('venues', 'artists', 'events', 'event_artists', 'menu_items', 'drink_items') 
        THEN '✅ Required'
        ELSE '❓ Optional'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY 
    CASE 
        WHEN table_name IN ('venues', 'artists', 'events', 'event_artists', 'menu_items', 'drink_items') 
        THEN 1 
        ELSE 2 
    END,
    table_name;

-- =====================================================
-- STEP 2: Check venues table structure
-- =====================================================

SELECT 
    'Venues Table Structure' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'venues' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- STEP 3: Check artists table structure
-- =====================================================

SELECT 
    'Artists Table Structure' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'artists' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- STEP 4: Check events table structure
-- =====================================================

SELECT 
    'Events Table Structure' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'events' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- STEP 5: Check foreign key constraints
-- =====================================================

SELECT 
    'Foreign Key Constraints' as check_type,
    tc.table_name, 
    tc.constraint_name, 
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_schema = 'public'
AND tc.table_name IN ('events', 'event_artists', 'menu_items', 'drink_items')
ORDER BY tc.table_name, tc.constraint_name;

-- =====================================================
-- STEP 6: Check RLS status
-- =====================================================

SELECT 
    'RLS Status' as check_type,
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    CASE 
        WHEN rowsecurity = true THEN '🔒 RLS Enabled'
        ELSE '🔓 RLS Disabled'
    END as status
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN ('venues', 'artists', 'events', 'event_artists', 'menu_items', 'drink_items')
ORDER BY tablename;

-- =====================================================
-- STEP 7: Check existing policies (if any)
-- =====================================================

SELECT 
    'RLS Policies' as check_type,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename IN ('venues', 'artists', 'events', 'event_artists', 'menu_items', 'drink_items')
ORDER BY tablename, policyname;

-- =====================================================
-- STEP 8: Test basic operations
-- =====================================================

-- Test if we can insert into venues table
-- (This will show the exact error if there's a problem)

-- Uncomment the next line to test venue insertion:
-- INSERT INTO venues (name, address) VALUES ('Test Venue', 'Test Address');

-- If the above works, clean it up:
-- DELETE FROM venues WHERE name = 'Test Venue';

-- =====================================================
-- STEP 9: Check storage bucket
-- =====================================================

SELECT 
    'Storage Buckets' as check_type,
    id as bucket_name,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'media';

-- =====================================================
-- STEP 10: Summary
-- =====================================================

SELECT 
    'Summary' as check_type,
    'Run this diagnostic to identify issues' as message,
    'Check each section above for problems' as action_needed;
