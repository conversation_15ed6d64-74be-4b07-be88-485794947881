-- Simple script to add missing columns to menu_items and drink_items tables
-- Run this first if you get "column does not exist" errors

-- Add missing columns to menu_items table
DO $$ 
BEGIN
    -- Add tags column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'menu_items' AND column_name = 'tags') THEN
        ALTER TABLE menu_items ADD COLUMN tags TEXT[] DEFAULT '{}';
        RAISE NOTICE 'Added tags column to menu_items table';
    ELSE
        RAISE NOTICE 'Tags column already exists in menu_items table';
    END IF;
    
    -- Add image_url column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'menu_items' AND column_name = 'image_url') THEN
        ALTER TABLE menu_items ADD COLUMN image_url TEXT;
        RAISE NOTICE 'Added image_url column to menu_items table';
    ELSE
        RAISE NOTICE 'Image_url column already exists in menu_items table';
    END IF;
    
    -- Add category column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'menu_items' AND column_name = 'category') THEN
        ALTER TABLE menu_items ADD COLUMN category TEXT;
        RAISE NOTICE 'Added category column to menu_items table';
    ELSE
        RAISE NOTICE 'Category column already exists in menu_items table';
    END IF;
    
    -- Add description column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'menu_items' AND column_name = 'description') THEN
        ALTER TABLE menu_items ADD COLUMN description TEXT;
        RAISE NOTICE 'Added description column to menu_items table';
    ELSE
        RAISE NOTICE 'Description column already exists in menu_items table';
    END IF;
END $$;

-- Add missing columns to drink_items table
DO $$ 
BEGIN
    -- Add tags column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'drink_items' AND column_name = 'tags') THEN
        ALTER TABLE drink_items ADD COLUMN tags TEXT[] DEFAULT '{}';
        RAISE NOTICE 'Added tags column to drink_items table';
    ELSE
        RAISE NOTICE 'Tags column already exists in drink_items table';
    END IF;
    
    -- Add price column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'drink_items' AND column_name = 'price') THEN
        ALTER TABLE drink_items ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00;
        RAISE NOTICE 'Added price column to drink_items table';
    ELSE
        RAISE NOTICE 'Price column already exists in drink_items table';
    END IF;
    
    -- Add image_url column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'drink_items' AND column_name = 'image_url') THEN
        ALTER TABLE drink_items ADD COLUMN image_url TEXT;
        RAISE NOTICE 'Added image_url column to drink_items table';
    ELSE
        RAISE NOTICE 'Image_url column already exists in drink_items table';
    END IF;
    
    -- Add category column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'drink_items' AND column_name = 'category') THEN
        ALTER TABLE drink_items ADD COLUMN category TEXT;
        RAISE NOTICE 'Added category column to drink_items table';
    ELSE
        RAISE NOTICE 'Category column already exists in drink_items table';
    END IF;
    
    -- Add description column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'drink_items' AND column_name = 'description') THEN
        ALTER TABLE drink_items ADD COLUMN description TEXT;
        RAISE NOTICE 'Added description column to drink_items table';
    ELSE
        RAISE NOTICE 'Description column already exists in drink_items table';
    END IF;
END $$;

-- Remove any restrictive check constraints
DO $$ 
DECLARE
    constraint_record RECORD;
BEGIN
    -- Remove constraints from menu_items
    FOR constraint_record IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'menu_items'::regclass 
        AND contype = 'c'
        AND conname LIKE '%_check'
    LOOP
        EXECUTE 'ALTER TABLE menu_items DROP CONSTRAINT ' || constraint_record.conname;
        RAISE NOTICE 'Dropped menu_items constraint: %', constraint_record.conname;
    END LOOP;
    
    -- Remove constraints from drink_items
    FOR constraint_record IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'drink_items'::regclass 
        AND contype = 'c'
        AND conname LIKE '%_check'
    LOOP
        EXECUTE 'ALTER TABLE drink_items DROP CONSTRAINT ' || constraint_record.conname;
        RAISE NOTICE 'Dropped drink_items constraint: %', constraint_record.conname;
    END LOOP;
END $$;

-- Verify table structures
SELECT 
    'menu_items structure' as table_info,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'menu_items' 
AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT 
    'drink_items structure' as table_info,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'drink_items' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Final status
SELECT 'MISSING COLUMNS ADDED AND CONSTRAINTS REMOVED!' as status;
