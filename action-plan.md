# Jazz & Dine Admin Portal – Action Plan

## 1. Authentication & Access Control
- Integrate Supabase Auth for admin login.
- Protect `/admin` route; redirect unauthenticated users to login.
- Implement login/logout UI.

**What is needed:**
- Supabase project URL and anon/public key.
- List of admin emails (or how to identify admins).
- Preferred authentication method.

---

## 2. Admin Layout & Navigation
- Desktop-first sidebar layout.
- Sidebar links: Dashboard, Events, Artists, Venues, Tickets, Groups, Settings.

**What is needed:**
- Logo/branding (optional).
- Any specific navigation items or icons.

---

## 3. Venues Management
- CRUD for venues: name, address, map link, image/logo (optional).
- List venues in a table.
- Venue selection dropdown in event creation/editing.

**What is needed:**
- Required fields for venues (name, address, map link, image/logo?).

---

## 4. Artists Management
- CRUD for artists: name, profession, image, bio.
- List artists in a table.
- Artist selection (multi-select) in event creation/editing.

**What is needed:**
- Any required fields for artists beyond those listed.

---

## 5. Event Management (with Menus & Ticket Pricing)
- CRUD for events: name, description, date/time, select venue, cover image, type.
- **Menu management embedded in event form:**
  - Add/edit/delete menu items (Dinner/Drinks tabs, name, description, image, tags, price, discounted price).
- **Ticket pricing fields in event form:**
  - Set price for Single and Group tickets.
- **Artist selection:**
  - Multi-select from pre-submitted artists.
- List events in a table.

**What is needed:**
- Dietary tags list for menu items.
- Any required fields for events, menu items, or tickets.

---

## 6. Tickets & Groups Management
- View all tickets (filter by event, type, status).
- View/manage group tables and members.
- Export guest lists as CSV.
- Mark guests as attended (check-in).

**What is needed:**
- Any custom fields for tickets/groups.
- Example CSV export format (optional).

---

## 7. Dashboard
- Quick stats: upcoming events, total tickets sold, revenue, recent orders, group bookings.

**What is needed:**
- Any specific metrics or KPIs you want highlighted.

---

## 8. Settings & Admin Management
- Manage admin users (optional).
- Update app settings (if any).

**What is needed:**
- Do you want a UI for managing admin users?
- Any app-wide settings you want configurable?

---

## 9. General
- Form validation, error handling, loading/empty states.
- Responsive design checks.
- Environment variable setup for Supabase.
- Documentation for future admins.

**What is needed:**
- Supabase/Stripe keys (for future payment features).
- Any specific error/empty state copy or branding.

---

## Summary Table

| Step | Feature                | What I Need From You                        |
|------|------------------------|---------------------------------------------|
| 1    | Auth & Access          | Supabase keys, admin emails, auth method    |
| 2    | Layout & Nav           | Logo/branding, nav items                    |
| 3    | Venues Mgmt            | Venue fields (name, address, map, image?)   |
| 4    | Artists Mgmt           | Artist fields (if any extra)                |
| 5    | Event Mgmt (Menus/Tix) | Dietary tags, required fields, sample data  |
| 6    | Tickets/Groups Mgmt    | Custom fields, CSV format                   |
| 7    | Dashboard              | Key metrics                                 |
| 8    | Settings/Admin Mgmt    | Admin UI needed? App settings?              |
| 9    | General                | Supabase keys, branding, copy               |

---

## Execution Flow
- Scaffold authentication and admin layout first.
- Build Venues and Artists management (so they’re available for event creation).
- Implement Event management, including embedded menu and ticket pricing.
- Build Tickets/Groups management and the Dashboard.
- Check in at each major step for feedback and data.

---

**This plan will be referenced for asynchronous, low-friction development.** 