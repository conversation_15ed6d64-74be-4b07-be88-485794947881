"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Utensils, Wine, Heart, Check, Gift, Save } from "lucide-react"
import type { MenuItem } from "@/lib/types"

interface SingleTicketFormProps {
  menuItems: MenuItem[]
  ticketPrice: number
  onSave?: () => void
  onSubmit: (data: {
    dinnerSelection: string
    selectedBottles: string[]
    totalAmount: number
  }) => void
}

export default function SingleTicketForm({ 
  menuItems, 
  ticketPrice, 
  onSave, 
  onSubmit 
}: SingleTicketFormProps) {
  const [selectedDinner, setSelectedDinner] = useState("")
  const [selectedBottles, setSelectedBottles] = useState<string[]>([])

  const dinnerItems = menuItems.filter(item => item.category === 'dinner')
  const drinkItems = menuItems.filter(item => item.category === 'drinks')

  const calculateTotal = () => {
    let total = ticketPrice
    
    selectedBottles.forEach(bottleName => {
      const bottle = drinkItems.find(item => item.name === bottleName)
      if (bottle && bottle.price) {
        const price = bottle.discounted_price || bottle.price
        total += Math.round(price * 0.85) // 15% discount
      }
    })
    
    return total
  }

  const handleBottleToggle = (bottleName: string) => {
    if (selectedBottles.includes(bottleName)) {
      setSelectedBottles(selectedBottles.filter(b => b !== bottleName))
    } else {
      setSelectedBottles([...selectedBottles, bottleName])
    }
  }

  const handleSubmit = () => {
    if (!selectedDinner) return
    
    onSubmit({
      dinnerSelection: selectedDinner,
      selectedBottles,
      totalAmount: calculateTotal()
    })
  }

  return (
    <Card className="bg-white/90 backdrop-blur-sm mb-8 shadow-xl border-0 animate-in zoom-in-95 duration-500">
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Heart className="w-5 h-5 text-purple-600 animate-pulse-subtle" />
          <h3 className="text-xl font-serif font-semibold text-gray-900">Complete Your Reservation</h3>
        </div>

        <div className="space-y-6">
          {/* Dinner Selection */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Utensils className="w-4 h-4 text-purple-600" />
              <Label className="text-sm font-semibold text-gray-700">Customize your dinner experience</Label>
            </div>
            <div className="space-y-3">
              {dinnerItems.map((item, index) => (
                <div
                  key={item.id}
                  className={`p-4 cursor-pointer transition-all duration-300 rounded-2xl border transform hover:scale-[1.01] active:scale-[0.99] animate-in slide-in-from-left-4 duration-300 ${
                    selectedDinner === item.name
                      ? "bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 shadow-lg"
                      : "bg-gradient-to-r from-gray-50 to-purple-50/30 border-gray-200 hover:from-white hover:to-purple-50 hover:shadow-md"
                  }`}
                  style={{ animationDelay: `${index * 100}ms` }}
                  onClick={() => setSelectedDinner(item.name)}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-2xl overflow-hidden shadow-lg ring-2 ring-purple-100">
                      <img
                        src={item.image_url || "/placeholder.svg"}
                        alt={item.name}
                        className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-serif font-semibold text-gray-900">{item.name}</h4>
                      <p className="text-sm text-gray-600">{item.description}</p>
                      <div className="flex gap-1 mt-1">
                        {item.dietary_tags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="text-xs bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 font-medium border-0"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    {selectedDinner === item.name && (
                      <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg animate-in zoom-in-50 duration-200">
                        <Check className="w-3 h-3 text-white" />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Bottle Selection */}
          {drinkItems.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Wine className="w-4 h-4 text-purple-600" />
                <Label className="text-sm font-semibold text-gray-700">Add Premium Bottles (Optional)</Label>
              </div>
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 mb-3 rounded-2xl border border-green-200/50 shadow-inner">
                <p className="text-sm text-green-700 font-semibold flex items-center gap-2">
                  <Gift className="w-4 h-4 animate-bounce-subtle" />
                  💰 Save 15% when you pre-order
                </p>
              </div>
              <div className="space-y-3">
                {drinkItems.slice(0, 4).map((item, index) => (
                  <div
                    key={item.id}
                    className={`p-4 cursor-pointer transition-all duration-300 rounded-2xl border transform hover:scale-[1.01] active:scale-[0.99] animate-in slide-in-from-right-4 duration-300 ${
                      selectedBottles.includes(item.name)
                        ? "bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 shadow-lg"
                        : "bg-gradient-to-r from-gray-50 to-purple-50/30 border-gray-200 hover:from-white hover:to-purple-50 hover:shadow-md"
                    }`}
                    style={{ animationDelay: `${index * 100}ms` }}
                    onClick={() => handleBottleToggle(item.name)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-2xl overflow-hidden shadow-lg ring-2 ring-purple-100">
                          <img
                            src={item.image_url || "/placeholder.svg"}
                            alt={item.name}
                            className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                          />
                        </div>
                        <div>
                          <h4 className="font-serif font-semibold text-gray-900">{item.name}</h4>
                          <p className="text-sm text-gray-600">{item.item_type}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        {item.price && (
                          <>
                            <div className="text-sm text-gray-400 line-through">${item.price}</div>
                            <div className="font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                              ${Math.round(item.price * 0.85)}
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Total */}
          <div className="bg-gradient-to-r from-gray-50 to-purple-50/50 p-4 rounded-2xl border border-purple-100/50 shadow-inner">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-700 font-medium">Dinner Ticket</span>
                <span className="font-semibold">${ticketPrice}</span>
              </div>
              {selectedBottles.map((bottleName) => {
                const bottle = drinkItems.find(d => d.name === bottleName)
                if (!bottle || !bottle.price) return null
                return (
                  <div key={bottleName} className="flex justify-between text-sm">
                    <span className="text-gray-600">{bottleName}</span>
                    <span className="font-semibold">${Math.round(bottle.price * 0.85)}</span>
                  </div>
                )
              })}
              <div className="border-t border-gray-200 pt-2 flex justify-between text-lg font-bold">
                <span className="text-gray-900">Total</span>
                <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  ${calculateTotal()}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {onSave && (
              <Button
                variant="outline"
                onClick={onSave}
                className="w-full flex items-center justify-center gap-2 text-purple-600 border-purple-200 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-lg py-3 transition-all duration-200 active:scale-[0.98]"
              >
                <Save className="w-4 h-4" />
                Save Progress
              </Button>
            )}
            <Button
              onClick={handleSubmit}
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!selectedDinner}
            >
              <Heart className="w-5 h-5 mr-2" />
              Proceed to Payment
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
