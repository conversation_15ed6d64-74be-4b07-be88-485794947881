import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabaseClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: ticketId } = await params;

    // Get the group ticket details
    const { data: ticket, error: ticketError } = await supabase
      .from('tickets')
      .select(`
        *,
        event:events(
          name,
          date,
          start_time,
          end_time,
          venue:venues(name, address)
        ),
        group_members(*)
      `)
      .eq('id', ticketId)
      .eq('ticket_type', 'group')
      .single();

    if (ticketError || !ticket) {
      return NextResponse.json(
        { error: 'Group table not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: ticket });
  } catch (error) {
    console.error('Error fetching group table:', error);
    return NextResponse.json(
      { error: 'Failed to fetch group table details' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: ticketId } = await params;
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Check if the group table exists and has space
    const { data: ticket, error: ticketError } = await supabase
      .from('tickets')
      .select(`
        *,
        group_members(*)
      `)
      .eq('id', ticketId)
      .eq('ticket_type', 'group')
      .single();

    if (ticketError || !ticket) {
      return NextResponse.json(
        { error: 'Group table not found' },
        { status: 404 }
      );
    }

    // Check if there's space in the group
    if (ticket.group_members.length >= ticket.group_size) {
      return NextResponse.json(
        { error: 'Group table is full' },
        { status: 400 }
      );
    }

    // Check if user is already in the group
    const existingMember = ticket.group_members.find(
      (member: any) => member.email.toLowerCase() === body.email.toLowerCase()
    );

    if (existingMember) {
      return NextResponse.json(
        { error: 'You are already part of this group' },
        { status: 400 }
      );
    }

    // Add the new group member
    const { data: newMember, error: memberError } = await supabase
      .from('group_members')
      .insert({
        ticket_id: ticketId,
        name: body.name,
        email: body.email,
        dinner_selection: body.dinner_selection,
        has_confirmed: true
      })
      .select()
      .single();

    if (memberError) {
      throw memberError;
    }

    return NextResponse.json({ 
      data: newMember,
      message: 'Successfully joined the group table!'
    }, { status: 201 });
  } catch (error) {
    console.error('Error joining group table:', error);
    return NextResponse.json(
      { error: 'Failed to join group table' },
      { status: 500 }
    );
  }
}
