"use client"

import { useState, useEffect } from "react"
import { MapPin } from "lucide-react"
import type { Venue } from "@/lib/types"

interface VenueSectionProps {
  venue: Venue
}

export default function VenueSection({ venue }: VenueSectionProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  const handleMapClick = () => {
    if (venue.map_link) {
      window.open(venue.map_link, '_blank')
    } else {
      // Fallback to Google Maps search
      const query = encodeURIComponent(venue.address)
      window.open(`https://www.google.com/maps/search/${query}`, '_blank')
    }
  }

  return (
    <div
      className={`mb-6 transform transition-all duration-700 delay-300 ease-out ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
      }`}
    >
      <div className="flex items-center gap-4 mb-4 group">
        <div className="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-br from-purple-100 to-pink-100 flex-shrink-0 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
          <img
            src={venue.logo_url || "/placeholder.svg"}
            alt={`${venue.name} logo`}
            className="w-full h-full object-cover"
          />
        </div>
        <div>
          <h2 className="font-serif font-bold text-xl text-gray-900 group-hover:text-purple-700 transition-colors duration-300">
            {venue.name}
          </h2>
          <div className="flex items-center gap-2 text-gray-600">
            <MapPin className="w-4 h-4 text-purple-500" />
            <span>{venue.address}</span>
          </div>
        </div>
      </div>

      {/* Interactive Map */}
      <div 
        className="w-full h-48 bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl mb-6 flex items-center justify-center shadow-inner border border-purple-100/50 hover:shadow-lg transition-all duration-300 cursor-pointer group"
        onClick={handleMapClick}
      >
        <div className="text-center text-gray-500 group-hover:text-purple-600 transition-colors duration-300">
          <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <MapPin className="w-6 h-6 text-white" />
          </div>
          <p className="text-sm font-semibold">Interactive Map</p>
          <p className="text-xs">Tap to view directions</p>
        </div>
      </div>
    </div>
  )
}
