-- Create venues table if it doesn't exist
-- Run this in Supabase SQL Editor if venues table is missing

-- Create venues table
CREATE TABLE IF NOT EXISTS venues (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    description TEXT,
    logo_url TEXT,
    image_url TEXT,
    map_link TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create artists table
CREATE TABLE IF NOT EXISTS artists (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    bio TEXT,
    image_url TEXT,
    instagram TEXT,
    facebook TEXT,
    youtube TEXT,
    website TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Disable RLS for simple setup
ALTER TABLE venues DISABLE ROW LEVEL SECURITY;
ALTER TABLE artists DISABLE ROW LEVEL SECURITY;

-- Test insertion
INSERT INTO venues (name, address, description) 
VALUES ('Test Venue', '123 Test Street', 'A test venue for verification')
ON CONFLICT DO NOTHING;

-- Verify it worked
SELECT 'Venues table test' as test, COUNT(*) as venue_count FROM venues;

-- Clean up test data
DELETE FROM venues WHERE name = 'Test Venue';

-- Final verification
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'venues' 
AND table_schema = 'public'
ORDER BY ordinal_position;
