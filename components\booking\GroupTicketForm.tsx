"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Sparkles, 
  Plus, 
  Minus, 
  Share2, 
  Save,
  Wine,
  Users,
  X
} from "lucide-react"
import type { MenuItem } from "@/lib/types"

interface GroupMember {
  name: string
  email: string
}

interface GroupTicketFormProps {
  menuItems: MenuItem[]
  ticketPrice: number
  onSave?: () => void
  onSubmit: (data: {
    groupName: string
    groupSize: number
    groupMembers: GroupMember[]
    selectedBottles: string[]
    totalAmount: number
  }) => void
}

export default function GroupTicketForm({ 
  menuItems, 
  ticketPrice, 
  onSave, 
  onSubmit 
}: GroupTicketFormProps) {
  const [groupName, setGroupName] = useState("")
  const [groupSize, setGroupSize] = useState(3)
  const [groupMembers, setGroupMembers] = useState<GroupMember[]>([
    { name: "", email: "" }
  ])
  const [selectedBottles, setSelectedBottles] = useState<string[]>([])

  const drinkItems = menuItems.filter(item => item.category === 'drinks')

  const calculateTotal = () => {
    let total = ticketPrice * groupSize
    
    selectedBottles.forEach(bottleName => {
      const bottle = drinkItems.find(item => item.name === bottleName)
      if (bottle && bottle.price) {
        const price = bottle.discounted_price || bottle.price
        total += Math.round(price * 0.85) // 15% discount
      }
    })
    
    return total
  }

  const handleBottleToggle = (bottleName: string) => {
    if (selectedBottles.includes(bottleName)) {
      setSelectedBottles(selectedBottles.filter(b => b !== bottleName))
    } else {
      setSelectedBottles([...selectedBottles, bottleName])
    }
  }

  const addGroupMember = () => {
    if (groupMembers.length < groupSize) {
      setGroupMembers([...groupMembers, { name: "", email: "" }])
    }
  }

  const removeGroupMember = (index: number) => {
    if (groupMembers.length > 1) {
      setGroupMembers(groupMembers.filter((_, i) => i !== index))
    }
  }

  const updateGroupMember = (index: number, field: 'name' | 'email', value: string) => {
    const updated = [...groupMembers]
    updated[index][field] = value
    setGroupMembers(updated)
  }

  const handleSubmit = () => {
    if (!groupName || groupMembers.some(member => !member.name)) return
    
    onSubmit({
      groupName,
      groupSize,
      groupMembers: groupMembers.filter(member => member.name.trim() !== ''),
      selectedBottles,
      totalAmount: calculateTotal()
    })
  }

  return (
    <Card className="bg-white/90 backdrop-blur-sm mb-8 shadow-xl border-0 animate-in zoom-in-95 duration-500">
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Sparkles className="w-5 h-5 text-blue-600 animate-pulse-subtle" />
          <h3 className="text-xl font-serif font-semibold text-gray-900">Create Your Table</h3>
        </div>

        <div className="space-y-6">
          {/* Group Details */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="groupName" className="text-sm font-semibold text-gray-700 mb-2 block">
                Table Name
              </Label>
              <Input
                id="groupName"
                placeholder="e.g., Sarah's Wine Night"
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
                className="h-12 text-base border-purple-200 focus:border-purple-400 focus:ring-purple-400 transition-all duration-200"
              />
            </div>

            <div>
              <Label className="text-sm font-semibold text-gray-700 mb-3 block">Number of Seats</Label>
              <div className="flex items-center justify-center gap-6 bg-gradient-to-r from-gray-50 to-purple-50/50 p-4 rounded-2xl border border-purple-100/50 shadow-inner">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setGroupSize(Math.max(3, groupSize - 1))}
                  className="w-10 h-10 rounded-full border-purple-200 hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 active:scale-95"
                >
                  <Minus className="w-4 h-4" />
                </Button>
                <span className="text-2xl font-bold text-gray-900 w-8 text-center">{groupSize}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setGroupSize(Math.min(8, groupSize + 1))}
                  className="w-10 h-10 rounded-full border-purple-200 hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 active:scale-95"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              <p className="text-sm text-gray-500 text-center mt-2">Minimum 3, maximum 8 people per table</p>
            </div>
          </div>

          {/* Group Members */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <Label className="text-sm font-semibold text-gray-700">Group Members</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={addGroupMember}
                disabled={groupMembers.length >= groupSize}
                className="text-purple-600 border-purple-200 hover:bg-purple-50"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Member
              </Button>
            </div>
            
            <div className="space-y-3">
              {groupMembers.map((member, index) => (
                <div key={index} className="flex gap-3 p-3 bg-gradient-to-r from-gray-50 to-blue-50/50 rounded-2xl border border-blue-100/50">
                  <div className="flex-1">
                    <Input
                      placeholder="Member name"
                      value={member.name}
                      onChange={(e) => updateGroupMember(index, 'name', e.target.value)}
                      className="mb-2 border-blue-200 focus:border-blue-400"
                    />
                    <Input
                      placeholder="Email (optional)"
                      type="email"
                      value={member.email}
                      onChange={(e) => updateGroupMember(index, 'email', e.target.value)}
                      className="border-blue-200 focus:border-blue-400"
                    />
                  </div>
                  {groupMembers.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeGroupMember(index)}
                      className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Premium Bottles */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Wine className="w-4 h-4 text-purple-600" />
              <Label className="text-sm font-semibold text-gray-700">Add Premium Bottles (Optional)</Label>
            </div>
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 mb-3 rounded-2xl border border-green-200/50 shadow-inner">
              <p className="text-sm text-green-700 font-semibold">💰 Save 15% when you pre-order</p>
            </div>
            <div className="space-y-3">
              {drinkItems.slice(0, 3).map((item, index) => (
                <div
                  key={item.id}
                  className={`p-4 cursor-pointer transition-all duration-300 rounded-2xl border transform hover:scale-[1.01] active:scale-[0.99] ${
                    selectedBottles.includes(item.name)
                      ? "bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 shadow-lg"
                      : "bg-gradient-to-r from-gray-50 to-purple-50/30 border-gray-200 hover:from-white hover:to-purple-50 hover:shadow-md"
                  }`}
                  onClick={() => handleBottleToggle(item.name)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-2xl overflow-hidden shadow-lg ring-2 ring-purple-100">
                        <img
                          src={item.image_url || "/placeholder.svg"}
                          alt={item.name}
                          className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                        />
                      </div>
                      <div>
                        <h4 className="font-serif font-semibold text-gray-900">{item.name}</h4>
                        <p className="text-sm text-gray-600">{item.item_type}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      {item.price && (
                        <>
                          <div className="text-sm text-gray-400 line-through">${item.price}</div>
                          <div className="font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                            ${Math.round(item.price * 0.85)}
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Total */}
          <div className="bg-gradient-to-r from-gray-50 to-purple-50/50 p-4 rounded-2xl border border-purple-100/50 shadow-inner">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-700 font-medium">Tickets ({groupSize} seats)</span>
                <span className="font-semibold">${ticketPrice * groupSize}</span>
              </div>
              {selectedBottles.map((bottleName) => {
                const bottle = drinkItems.find(item => item.name === bottleName)
                if (!bottle || !bottle.price) return null
                return (
                  <div key={bottleName} className="flex justify-between text-sm">
                    <span className="text-gray-600">{bottleName}</span>
                    <span className="font-semibold">${Math.round(bottle.price * 0.85)}</span>
                  </div>
                )
              })}
              <div className="border-t border-gray-200 pt-2 flex justify-between text-lg font-bold">
                <span className="text-gray-900">Total</span>
                <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  ${calculateTotal()}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {onSave && (
              <Button
                variant="outline"
                onClick={onSave}
                className="w-full flex items-center justify-center gap-2 text-purple-600 border-purple-200 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-lg py-3 transition-all duration-200 active:scale-[0.98]"
              >
                <Save className="w-4 h-4" />
                Save Progress
              </Button>
            )}
            <Button
              onClick={handleSubmit}
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!groupName || groupMembers.some(member => !member.name)}
            >
              <Share2 className="w-5 h-5 mr-2" />
              Create Table & Get Share Link
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
