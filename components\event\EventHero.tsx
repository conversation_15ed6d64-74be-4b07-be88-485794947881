"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, Clock, Sparkles } from "lucide-react"
import type { Event } from "@/lib/types"

interface EventHeroProps {
  event: Event
  onJoinClick: () => void
}

export default function EventHero({ event, onJoinClick }: EventHeroProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  const formatTime = (startTime: string, endTime: string) => {
    const start = new Date(`2000-01-01T${startTime}`)
    const end = new Date(`2000-01-01T${endTime}`)
    
    const formatTime = (time: Date) => {
      return time.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      })
    }
    
    return `${formatTime(start)} - ${formatTime(end)}`
  }

  return (
    <section id="about" className="relative">
      <div className="relative h-[60vh] overflow-hidden">
        <img
          src={event.cover_image_url || "/placeholder.svg"}
          alt={event.name}
          className="w-full h-full object-cover scale-105 transition-transform duration-700 ease-out"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20" />

        <div
          className={`absolute bottom-0 left-0 right-0 px-4 sm:px-6 pb-6 sm:pb-8 text-white transform transition-all duration-1000 ease-out ${
            isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
          }`}
        >
          <Badge className="mb-3 sm:mb-4 bg-white/20 text-white border-0 backdrop-blur-md font-medium shadow-lg animate-pulse-subtle">
            {event.event_type}
          </Badge>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-serif font-bold mb-2 sm:mb-3 leading-tight bg-gradient-to-r from-white to-purple-100 bg-clip-text text-transparent">
            {event.name}
          </h1>
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-sm mb-4">
            <div className="flex items-center gap-2 group">
              <Calendar className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
              <span>{formatDate(event.date)}</span>
            </div>
            <div className="flex items-center gap-2 group">
              <Clock className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
              <span>{formatTime(event.start_time, event.end_time)}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 sm:px-6 py-6">
        <div
          className={`transform transition-all duration-700 delay-500 ease-out ${
            isVisible ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
          }`}
        >
          <div className="space-y-4">
            <p className="text-gray-700 leading-relaxed text-base">
              {event.short_description}
            </p>
            {event.description && (
              <p className="text-gray-700 leading-relaxed text-base">
                {event.description}
              </p>
            )}
          </div>

          <Button
            onClick={onJoinClick}
            className="w-full mt-8 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]"
          >
            <Sparkles className="w-5 h-5 mr-2" />
            Join Now
          </Button>
        </div>
      </div>
    </section>
  )
}
