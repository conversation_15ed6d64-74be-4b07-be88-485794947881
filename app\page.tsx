"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Calendar,
  Clock,
  MapPin,
  Music,
  Search,
  Filter,
  Sparkles,
  ArrowRight,
} from "lucide-react"
import { createSlug, formatEventDate, formatEventTime } from "@/lib/utils"
import type { Event } from "@/lib/types"

export default function EventsListingPage() {
  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("all")

  useEffect(() => {
    fetchEvents()
  }, [])

  const fetchEvents = async () => {
    try {
      const response = await fetch('/api/events?include=relations&upcoming=true')
      const result = await response.json()
      if (result.data) {
        setEvents(result.data)
      }
    } catch (error) {
      console.error('Error fetching events:', error)
    } finally {
      setLoading(false)
    }
  }



  const filteredEvents = events.filter(event => {
    const matchesSearch = event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.venue?.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterType === "all" || event.event_type.toLowerCase().includes(filterType.toLowerCase())
    
    return matchesSearch && matchesFilter
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading events...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-serif font-bold text-gray-900 mb-2">
              Jazz & Dine Events
            </h1>
            <p className="text-lg text-gray-600">
              Premium pop-up dining experiences with live music
            </p>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Search events, venues, or descriptions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 text-base border-gray-300 focus:border-purple-400 focus:ring-purple-400"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="pl-10 pr-8 h-12 text-base border border-gray-300 rounded-md focus:border-purple-400 focus:ring-purple-400 bg-white appearance-none cursor-pointer"
              >
                <option value="all">All Events</option>
                <option value="live music">Live Music</option>
                <option value="special">Special Events</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Events Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {filteredEvents.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Calendar className="w-10 h-10 text-purple-600" />
            </div>
            <h3 className="text-2xl font-serif font-bold text-gray-900 mb-4">
              {searchTerm || filterType !== "all" ? "No events found" : "No events available"}
            </h3>
            <p className="text-gray-600 mb-8">
              {searchTerm || filterType !== "all" 
                ? "Try adjusting your search or filter criteria" 
                : "Check back soon for upcoming events"}
            </p>
            {(searchTerm || filterType !== "all") && (
              <Button
                onClick={() => {
                  setSearchTerm("")
                  setFilterType("all")
                }}
                variant="outline"
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredEvents.map((event, index) => (
              <Card 
                key={event.id} 
                className="group hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] bg-white/80 backdrop-blur-sm border-0 shadow-lg overflow-hidden animate-in slide-in-from-bottom-4 duration-500"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={event.cover_image_url || "/placeholder.svg"}
                    alt={event.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-white/20 text-white border-0 backdrop-blur-md font-medium shadow-lg">
                      {event.event_type}
                    </Badge>
                  </div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <h3 className="text-xl font-serif font-bold text-white mb-1 line-clamp-2">
                      {event.name}
                    </h3>
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Date and Time */}
                    <div className="flex items-center gap-2 text-gray-600">
                      <Calendar className="w-4 h-4 text-purple-500" />
                      <span className="text-sm font-medium">{formatEventDate(event.date)}</span>
                    </div>

                    <div className="flex items-center gap-2 text-gray-600">
                      <Clock className="w-4 h-4 text-purple-500" />
                      <span className="text-sm">{formatEventTime(event.start_time, event.end_time)}</span>
                    </div>

                    {/* Venue */}
                    {event.venue && (
                      <div className="flex items-center gap-2 text-gray-600">
                        <MapPin className="w-4 h-4 text-purple-500" />
                        <span className="text-sm">{event.venue.name}</span>
                      </div>
                    )}

                    {/* Artists */}
                    {event.artists && event.artists.length > 0 && (
                      <div className="flex items-center gap-2 text-gray-600">
                        <Music className="w-4 h-4 text-purple-500" />
                        <span className="text-sm">
                          {event.artists.slice(0, 2).map((ea: any) => ea.artist.name).join(", ")}
                          {event.artists.length > 2 && ` +${event.artists.length - 2} more`}
                        </span>
                      </div>
                    )}

                    {/* Description */}
                    <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
                      {event.short_description || event.description}
                    </p>

                    {/* Pricing */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div>
                        <span className="text-sm text-gray-500">From</span>
                        <div className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                          ${Math.min(event.single_ticket_price, event.group_ticket_price)}
                        </div>
                      </div>
                      <Link href={`/event/${createSlug(event.name)}`}>
                        <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 group">
                          <Sparkles className="w-4 h-4 mr-2 group-hover:animate-pulse" />
                          Book Now
                          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
