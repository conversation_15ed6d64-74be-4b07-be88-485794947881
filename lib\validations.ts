import { z } from "zod"

// Base validation schemas
const urlSchema = z.string().url("Please enter a valid URL").optional().or(z.literal(""))

const imageUrlSchema = z.string()
  .optional()
  .refine((val) => {
    if (!val || val === "") return true
    try {
      new URL(val)
      return true
    } catch {
      return false
    }
  }, "Please enter a valid image URL")

// Venue validation schema
export const venueSchema = z.object({
  name: z.string()
    .min(1, "Venue name is required")
    .max(100, "Venue name must be less than 100 characters"),
  
  address: z.string()
    .min(1, "Address is required")
    .max(200, "Address must be less than 200 characters"),
  
  description: z.string()
    .max(500, "Description must be less than 500 characters")
    .optional(),
  
  logo_url: imageUrlSchema,
  
  image_url: imageUrlSchema,
  
  map_link: urlSchema
})

// Artist validation schema
export const artistSchema = z.object({
  name: z.string()
    .min(1, "Artist name is required")
    .max(100, "Artist name must be less than 100 characters"),
  
  role: z.string()
    .min(1, "Artist role is required")
    .max(100, "Role must be less than 100 characters"),
  
  bio: z.string()
    .max(1000, "Bio must be less than 1000 characters")
    .optional(),
  
  image_url: imageUrlSchema,
  
  instagram: urlSchema.refine((val) => {
    if (!val || val === "") return true
    return val.includes("instagram.com")
  }, "Please enter a valid Instagram URL"),
  
  facebook: urlSchema.refine((val) => {
    if (!val || val === "") return true
    return val.includes("facebook.com") || val.includes("fb.com")
  }, "Please enter a valid Facebook URL"),
  
  youtube: urlSchema.refine((val) => {
    if (!val || val === "") return true
    return val.includes("youtube.com") || val.includes("youtu.be")
  }, "Please enter a valid YouTube URL"),
  
  website: urlSchema
})

// Menu item validation schema
export const menuItemSchema = z.object({
  name: z.string()
    .min(1, "Menu item name is required")
    .max(100, "Name must be less than 100 characters"),
  
  description: z.string()
    .max(500, "Description must be less than 500 characters")
    .optional(),
  
  category: z.string()
    .max(50, "Category must be less than 50 characters")
    .optional(),
  
  image_url: imageUrlSchema,
  
  tags: z.array(z.string()).optional()
})

// Drink item validation schema
export const drinkItemSchema = z.object({
  name: z.string()
    .min(1, "Drink name is required")
    .max(100, "Name must be less than 100 characters"),
  
  description: z.string()
    .max(500, "Description must be less than 500 characters")
    .optional(),
  
  price: z.number()
    .min(0, "Price must be 0 or greater")
    .max(1000, "Price must be less than $1000"),
  
  category: z.string()
    .max(50, "Category must be less than 50 characters")
    .optional(),
  
  image_url: imageUrlSchema,
  
  tags: z.array(z.string()).optional()
})

// Event validation schema
export const eventSchema = z.object({
  name: z.string()
    .min(1, "Event name is required")
    .max(100, "Event name must be less than 100 characters"),
  
  description: z.string()
    .min(1, "Description is required")
    .max(1000, "Description must be less than 1000 characters"),
  
  date: z.string()
    .min(1, "Date is required")
    .refine((val) => {
      const date = new Date(val)
      return date >= new Date(new Date().setHours(0, 0, 0, 0))
    }, "Event date cannot be in the past"),
  
  start_time: z.string()
    .min(1, "Start time is required")
    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Please enter a valid time (HH:MM)"),
  
  end_time: z.string()
    .min(1, "End time is required")
    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Please enter a valid time (HH:MM)"),
  
  cover_image_url: imageUrlSchema,
  
  event_type: z.enum(["dinner_show", "cocktail_event", "wine_tasting", "private_dining"])
    .default("dinner_show"),
  
  single_ticket_price: z.number()
    .min(0.01, "Single ticket price must be greater than 0")
    .max(1000, "Price must be less than $1000"),
  
  group_ticket_price: z.number()
    .min(0.01, "Group ticket price must be greater than 0")
    .max(1000, "Price must be less than $1000"),
  
  max_capacity: z.number()
    .min(1, "Capacity must be at least 1")
    .max(1000, "Capacity must be less than 1000"),
  
  venue_id: z.string()
    .optional()
    .nullable(),
  
  artist_ids: z.array(z.string())
    .default([]),
  
  menu_items: z.array(menuItemSchema)
    .default([]),
  
  drink_items: z.array(drinkItemSchema)
    .default([])
}).refine((data) => {
  // Validate that end time is after start time
  const startTime = new Date(`2000-01-01T${data.start_time}:00`)
  const endTime = new Date(`2000-01-01T${data.end_time}:00`)
  return endTime > startTime
}, {
  message: "End time must be after start time",
  path: ["end_time"]
}).refine((data) => {
  // Validate that group price is less than or equal to single price
  return data.group_ticket_price <= data.single_ticket_price
}, {
  message: "Group ticket price should be less than or equal to single ticket price",
  path: ["group_ticket_price"]
})

// Export types
export type VenueFormData = z.infer<typeof venueSchema>
export type ArtistFormData = z.infer<typeof artistSchema>
export type EventFormData = z.infer<typeof eventSchema>
export type MenuItemFormData = z.infer<typeof menuItemSchema>
export type DrinkItemFormData = z.infer<typeof drinkItemSchema>

// Validation helper functions
export const validateVenue = (data: unknown) => venueSchema.safeParse(data)
export const validateArtist = (data: unknown) => artistSchema.safeParse(data)
export const validateEvent = (data: unknown) => eventSchema.safeParse(data)
export const validateMenuItem = (data: unknown) => menuItemSchema.safeParse(data)
export const validateDrinkItem = (data: unknown) => drinkItemSchema.safeParse(data)

// Error formatting helper
export const formatZodErrors = (errors: z.ZodError): Record<string, string> => {
  const formattedErrors: Record<string, string> = {}
  
  errors.errors.forEach((error) => {
    const path = error.path.join('.')
    formattedErrors[path] = error.message
  })
  
  return formattedErrors
}
