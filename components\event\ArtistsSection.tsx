"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Music, ChevronDown, Instagram, Twitter, Globe } from "lucide-react"
import type { Artist } from "@/lib/types"

interface ArtistsSectionProps {
  artists: Artist[]
}

export default function ArtistsSection({ artists }: ArtistsSectionProps) {
  if (!artists || artists.length === 0) {
    return null
  }

  return (
    <section id="artists" className="px-4 sm:px-6 py-8 bg-gradient-to-br from-gray-50 to-purple-50/50">
      <div className="flex items-center gap-2 mb-2">
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
          <Music className="w-4 h-4 text-white" />
        </div>
        <h2 className="text-2xl font-serif font-bold text-gray-900">Meet the Artists</h2>
      </div>
      <p className="text-gray-600 mb-6">Tonight's talented performers will create the perfect ambiance</p>

      <div className="space-y-4">
        {artists.map((artist, index) => (
          <Dialog key={artist.id}>
            <DialogTrigger asChild>
              <div
                className={`bg-white/80 backdrop-blur-sm p-4 hover:bg-white hover:shadow-lg transition-all duration-300 cursor-pointer border border-white/50 rounded-2xl transform hover:scale-[1.02] animate-in slide-in-from-left-4 duration-500`}
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full overflow-hidden flex-shrink-0 shadow-lg ring-2 ring-purple-100 hover:ring-purple-300 transition-all duration-300">
                    <img
                      src={artist.image_url || "/placeholder.svg"}
                      alt={artist.name}
                      className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-serif font-semibold text-gray-900 text-lg mb-1 hover:text-purple-700 transition-colors duration-200">
                      {artist.name}
                    </h3>
                    <p className="text-purple-600 font-medium mb-2">{artist.role}</p>
                    <p className="text-gray-600 text-sm line-clamp-2 leading-relaxed">{artist.bio}</p>
                  </div>
                  <ChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0 group-hover:text-purple-500 transition-colors duration-200" />
                </div>
              </div>
            </DialogTrigger>

            <DialogContent className="max-w-md mx-auto bg-white/95 backdrop-blur-lg border-0 shadow-2xl animate-in zoom-in-95 duration-300">
              <DialogHeader className="text-left space-y-4">
                <div className="flex items-center gap-4">
                  <div className="w-20 h-20 rounded-full overflow-hidden shadow-xl ring-4 ring-purple-100">
                    <img
                      src={artist.image_url || "/placeholder.svg"}
                      alt={artist.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <DialogTitle className="text-xl font-serif font-bold text-gray-900">{artist.name}</DialogTitle>
                    <p className="text-purple-600 font-semibold">{artist.role}</p>
                  </div>
                </div>
              </DialogHeader>

              <p className="text-gray-700 leading-relaxed mb-6">{artist.bio}</p>

              <div className="flex gap-3">
                {artist.instagram && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 bg-gradient-to-r from-pink-50 to-purple-50 border-purple-200 hover:from-pink-100 hover:to-purple-100 transition-all duration-200"
                    onClick={() => window.open(`https://instagram.com/${artist.instagram.replace('@', '')}`, '_blank')}
                  >
                    <Instagram className="w-4 h-4 mr-2" />
                    Instagram
                  </Button>
                )}
                {artist.twitter && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 hover:from-blue-100 hover:to-purple-100 transition-all duration-200"
                    onClick={() => window.open(`https://twitter.com/${artist.twitter.replace('@', '')}`, '_blank')}
                  >
                    <Twitter className="w-4 h-4 mr-2" />
                    Twitter
                  </Button>
                )}
                {artist.website && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 bg-gradient-to-r from-gray-50 to-purple-50 border-gray-200 hover:from-gray-100 hover:to-purple-100 transition-all duration-200"
                    onClick={() => window.open(artist.website.startsWith('http') ? artist.website : `https://${artist.website}`, '_blank')}
                  >
                    <Globe className="w-4 h-4 mr-2" />
                    Website
                  </Button>
                )}
              </div>
            </DialogContent>
          </Dialog>
        ))}
      </div>
    </section>
  )
}
