-- Test script to verify all Jazz & Dine relationships are working
-- Run this AFTER creating tables to test data integrity

-- =====================================================
-- STEP 1: Insert test data
-- =====================================================

-- Insert test venue
INSERT INTO venues (name, address, description) 
VALUES ('Test Jazz Club', '123 Music Street, Jazz City', 'A cozy venue for intimate performances')
ON CONFLICT DO NOTHING;

-- Insert test artists
INSERT INTO artists (name, role, bio) 
VALUES 
    ('<PERSON> Jazz', 'Pianist', 'Renowned jazz pianist with 20 years experience'),
    ('<PERSON>', 'Saxophonist', 'Smooth jazz saxophonist and composer')
ON CONFLICT DO NOTHING;

-- Insert test event
INSERT INTO events (name, description, date, start_time, end_time, venue_id, event_type, status) 
SELECT 
    'Test Jazz Night',
    'An evening of smooth jazz and fine dining',
    CURRENT_DATE + INTERVAL '7 days',
    '19:00:00',
    '22:00:00',
    v.id,
    'dinner_show',
    'active'
FROM venues v 
WHERE v.name = 'Test Jazz Club'
ON CONFLICT DO NOTHING;

-- =====================================================
-- STEP 2: Test relationships
-- =====================================================

-- Link artists to event
INSERT INTO event_artists (event_id, artist_id)
SELECT e.id, a.id
FROM events e, artists a
WHERE e.name = 'Test Jazz Night' 
AND a.name IN ('John Jazz', 'Sarah Sax')
ON CONFLICT DO NOTHING;

-- Add test menu items
INSERT INTO menu_items (event_id, name, description, category)
SELECT 
    e.id,
    'Grilled Salmon',
    'Fresh Atlantic salmon with herbs',
    'Main Course'
FROM events e 
WHERE e.name = 'Test Jazz Night'
ON CONFLICT DO NOTHING;

INSERT INTO menu_items (event_id, name, description, category)
SELECT 
    e.id,
    'Chocolate Mousse',
    'Rich chocolate dessert',
    'Dessert'
FROM events e 
WHERE e.name = 'Test Jazz Night'
ON CONFLICT DO NOTHING;

-- Add test drink items
INSERT INTO drink_items (event_id, name, description, price, category)
SELECT 
    e.id,
    'House Wine',
    'Selection of red and white wines',
    12.00,
    'Wine'
FROM events e 
WHERE e.name = 'Test Jazz Night'
ON CONFLICT DO NOTHING;

INSERT INTO drink_items (event_id, name, description, price, category)
SELECT 
    e.id,
    'Jazz Martini',
    'Classic martini with a twist',
    15.00,
    'Cocktail'
FROM events e 
WHERE e.name = 'Test Jazz Night'
ON CONFLICT DO NOTHING;

-- =====================================================
-- STEP 3: Verify all relationships work
-- =====================================================

-- Test 1: Get event with all related data
SELECT 
    'Event with all relationships' as test_name,
    e.name as event_name,
    v.name as venue_name,
    COUNT(DISTINCT ea.artist_id) as artist_count,
    COUNT(DISTINCT mi.id) as menu_item_count,
    COUNT(DISTINCT di.id) as drink_item_count
FROM events e
LEFT JOIN venues v ON e.venue_id = v.id
LEFT JOIN event_artists ea ON e.id = ea.event_id
LEFT JOIN menu_items mi ON e.id = mi.event_id
LEFT JOIN drink_items di ON e.id = di.event_id
WHERE e.name = 'Test Jazz Night'
GROUP BY e.id, e.name, v.name;

-- Test 2: Get artists for event
SELECT 
    'Artists for event' as test_name,
    e.name as event_name,
    a.name as artist_name,
    a.role as artist_role
FROM events e
JOIN event_artists ea ON e.id = ea.event_id
JOIN artists a ON ea.artist_id = a.id
WHERE e.name = 'Test Jazz Night';

-- Test 3: Get menu items for event
SELECT 
    'Menu items for event' as test_name,
    e.name as event_name,
    mi.name as menu_item_name,
    mi.category as menu_category
FROM events e
JOIN menu_items mi ON e.id = mi.event_id
WHERE e.name = 'Test Jazz Night';

-- Test 4: Get drink items for event
SELECT 
    'Drink items for event' as test_name,
    e.name as event_name,
    di.name as drink_name,
    di.price as drink_price,
    di.category as drink_category
FROM events e
JOIN drink_items di ON e.id = di.event_id
WHERE e.name = 'Test Jazz Night';

-- =====================================================
-- STEP 4: Cleanup (optional)
-- =====================================================

-- Uncomment these lines to clean up test data:
-- DELETE FROM drink_items WHERE event_id IN (SELECT id FROM events WHERE name = 'Test Jazz Night');
-- DELETE FROM menu_items WHERE event_id IN (SELECT id FROM events WHERE name = 'Test Jazz Night');
-- DELETE FROM event_artists WHERE event_id IN (SELECT id FROM events WHERE name = 'Test Jazz Night');
-- DELETE FROM events WHERE name = 'Test Jazz Night';
-- DELETE FROM artists WHERE name IN ('John Jazz', 'Sarah Sax');
-- DELETE FROM venues WHERE name = 'Test Jazz Club';
