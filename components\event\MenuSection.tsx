"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Utensils, Wine, Gift } from "lucide-react"
import type { MenuItem } from "@/lib/types"

interface MenuSectionProps {
  menuItems: MenuItem[]
}

export default function MenuSection({ menuItems }: MenuSectionProps) {
  const dinnerItems = menuItems.filter(item => item.category === 'dinner')
  const drinkItems = menuItems.filter(item => item.category === 'drinks')

  if (menuItems.length === 0) {
    return null
  }

  return (
    <section id="menu" className="px-4 sm:px-6 py-8 bg-white">
      <div className="flex items-center gap-2 mb-2">
        <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-lg">
          <Utensils className="w-4 h-4 text-white" />
        </div>
        <h2 className="text-2xl font-serif font-bold text-gray-900">Tonight's Menu</h2>
      </div>
      <p className="text-gray-600 mb-6">Carefully curated dishes paired with premium selections</p>

      <Tabs defaultValue="dinner" className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-gradient-to-r from-gray-100 to-purple-100/50 p-1 mb-6 shadow-inner">
          <TabsTrigger
            value="dinner"
            className="data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-lg font-semibold transition-all duration-200"
          >
            <Utensils className="w-4 h-4 mr-2" />
            Dinner
          </TabsTrigger>
          <TabsTrigger
            value="drinks"
            className="data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-lg font-semibold transition-all duration-200"
          >
            <Wine className="w-4 h-4 mr-2" />
            Drinks
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dinner" className="space-y-4">
          {dinnerItems.map((item, index) => (
            <div
              key={item.id}
              className={`bg-gradient-to-r from-gray-50 to-orange-50/50 p-4 hover:from-white hover:to-orange-50 hover:shadow-lg transition-all duration-300 rounded-2xl border border-orange-100/50 transform hover:scale-[1.01] animate-in slide-in-from-right-4 duration-500`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="flex gap-4">
                <div className="w-20 h-20 rounded-2xl overflow-hidden flex-shrink-0 shadow-lg ring-2 ring-orange-100 hover:ring-orange-200 transition-all duration-300">
                  <img
                    src={item.image_url || "/placeholder.svg"}
                    alt={item.name}
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-serif font-semibold text-gray-900 text-lg mb-2 hover:text-orange-700 transition-colors duration-200">
                    {item.name}
                  </h3>
                  <p className="text-gray-600 mb-3 leading-relaxed">{item.description}</p>
                  <div className="flex gap-2">
                    {item.dietary_tags.map((tag) => (
                      <Badge
                        key={tag}
                        variant="secondary"
                        className="text-xs bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 font-medium border-0 shadow-sm"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </TabsContent>

        <TabsContent value="drinks" className="space-y-4">
          <div className="bg-gradient-to-r from-purple-50 via-pink-50 to-purple-50 p-4 rounded-2xl mb-4 border border-purple-200/50 shadow-inner">
            <div className="flex items-center gap-2 mb-2">
              <Gift className="w-5 h-5 text-purple-600 animate-bounce-subtle" />
              <span className="font-semibold text-purple-900">Purchase a bottle for your table and save!</span>
            </div>
            <p className="text-purple-700 text-sm">Pre-order premium bottles and enjoy 15% off regular pricing</p>
          </div>
          {drinkItems.map((item, index) => (
            <div
              key={item.id}
              className={`bg-gradient-to-r from-gray-50 to-purple-50/50 p-4 hover:from-white hover:to-purple-50 hover:shadow-lg transition-all duration-300 rounded-2xl border border-purple-100/50 transform hover:scale-[1.01] animate-in slide-in-from-left-4 duration-500`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="flex gap-4">
                <div className="w-20 h-20 rounded-2xl overflow-hidden flex-shrink-0 shadow-lg ring-2 ring-purple-100 hover:ring-purple-200 transition-all duration-300">
                  <img
                    src={item.image_url || "/placeholder.svg"}
                    alt={item.name}
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-serif font-semibold text-gray-900 text-lg hover:text-purple-700 transition-colors duration-200">
                      {item.name}
                    </h3>
                    {item.price && (
                      <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        ${item.discounted_price || item.price}
                      </span>
                    )}
                  </div>
                  {item.item_type && (
                    <Badge
                      variant="secondary"
                      className="text-xs bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 font-medium border-0 shadow-sm mb-2"
                    >
                      {item.item_type}
                    </Badge>
                  )}
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
              </div>
            </div>
          ))}
        </TabsContent>
      </Tabs>
    </section>
  )
}
